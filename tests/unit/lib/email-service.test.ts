/**
 * Email Service Unit Tests
 * Kiểm tra unit cho email notification service
 */

import { EmailService } from "@/lib/email-service";

// Mock nodemailer
const mockSendMail = jest.fn();
const mockCreateTransporter = jest.fn(() => ({
  sendMail: mockSendMail,
  verify: jest.fn(),
}));

jest.mock("nodemailer", () => ({
  createTransporter: mockCreateTransporter,
}));

// Mock environment variables
const originalEnv = process.env;

describe("EmailService", () => {
  let emailService: EmailService;

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset singleton instance
    (EmailService as any).instance = undefined;
    emailService = EmailService.getInstance();
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
    process.env = originalEnv;
  });

  describe("Singleton Pattern", () => {
    it("should return the same instance", () => {
      const instance1 = EmailService.getInstance();
      const instance2 = EmailService.getInstance();

      expect(instance1).toBe(instance2);
    });
  });

  describe("Initialization", () => {
    it("should initialize with valid SMTP configuration", async () => {
      process.env.SMTP_HOST = "smtp.gmail.com";
      process.env.SMTP_PORT = "587";
      process.env.SMTP_USER = "<EMAIL>";
      process.env.SMTP_PASS = "password";
      process.env.SMTP_FROM = "<EMAIL>";

      const result = await emailService.initialize();

      expect(result).toBe(true);
      expect(mockCreateTransporter).toHaveBeenCalledWith({
        host: "smtp.gmail.com",
        port: 587,
        secure: false,
        auth: {
          user: "<EMAIL>",
          pass: "password",
        },
      });
    });

    it("should fail initialization with missing configuration", async () => {
      // Clear environment variables
      delete process.env.SMTP_HOST;
      delete process.env.SMTP_USER;

      const result = await emailService.initialize();

      expect(result).toBe(false);
    });

    it("should handle transporter verification failure", async () => {
      process.env.SMTP_HOST = "smtp.gmail.com";
      process.env.SMTP_PORT = "587";
      process.env.SMTP_USER = "<EMAIL>";
      process.env.SMTP_PASS = "password";

      const mockVerify = jest.fn().mockRejectedValue(new Error("Connection failed"));
      mockCreateTransporter.mockReturnValue({
        sendMail: mockSendMail,
        verify: mockVerify,
      });

      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      const result = await emailService.initialize();

      expect(result).toBe(false);
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Failed to verify email transporter:",
        expect.any(Error)
      );
    });
  });

  describe("Email Sending", () => {
    const mockNotificationData = {
      notification: {
        id: "notif-123",
        title: "Test Notification",
        message: "This is a test notification",
        type: "INFO" as const,
        priority: "NORMAL" as const,
        createdAt: "2024-01-01T00:00:00Z",
      },
      recipient: {
        name: "Test User",
        email: "<EMAIL>",
      },
    };

    beforeEach(async () => {
      // Setup valid configuration
      process.env.SMTP_HOST = "smtp.gmail.com";
      process.env.SMTP_PORT = "587";
      process.env.SMTP_USER = "<EMAIL>";
      process.env.SMTP_PASS = "password";
      process.env.SMTP_FROM = "<EMAIL>";

      await emailService.initialize();
    });

    describe("sendNotificationEmail", () => {
      it("should send notification email successfully", async () => {
        mockSendMail.mockResolvedValue({ messageId: "test-message-id" });

        const result = await emailService.sendNotificationEmail(mockNotificationData);

        expect(result).toBe(true);
        expect(mockSendMail).toHaveBeenCalledWith({
          from: "<EMAIL>",
          to: "<EMAIL>",
          subject: "NS Shop Admin - Test Notification",
          html: expect.stringContaining("Test Notification"),
        });
      });

      it("should handle email sending failure", async () => {
        mockSendMail.mockRejectedValue(new Error("SMTP error"));
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

        const result = await emailService.sendNotificationEmail(mockNotificationData);

        expect(result).toBe(false);
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          "Failed to send notification email:",
          expect.any(Error)
        );
      });

      it("should not send email if service not initialized", async () => {
        // Create new instance without initialization
        (EmailService as any).instance = undefined;
        const uninitializedService = EmailService.getInstance();

        const result = await uninitializedService.sendNotificationEmail(mockNotificationData);

        expect(result).toBe(false);
        expect(mockSendMail).not.toHaveBeenCalled();
      });

      it("should generate correct email content for different notification types", async () => {
        const warningNotification = {
          ...mockNotificationData,
          notification: {
            ...mockNotificationData.notification,
            type: "WARNING" as const,
            title: "Warning Alert",
            message: "This is a warning message",
          },
        };

        mockSendMail.mockResolvedValue({ messageId: "test-message-id" });

        await emailService.sendNotificationEmail(warningNotification);

        expect(mockSendMail).toHaveBeenCalledWith({
          from: "<EMAIL>",
          to: "<EMAIL>",
          subject: "NS Shop Admin - Warning Alert",
          html: expect.stringContaining("Warning Alert"),
        });
      });

      it("should handle special characters in notification content", async () => {
        const specialCharNotification = {
          ...mockNotificationData,
          notification: {
            ...mockNotificationData.notification,
            title: "Thông báo đặc biệt với ký tự Việt Nam",
            message: "Nội dung có ký tự đặc biệt: <script>alert('test')</script>",
          },
        };

        mockSendMail.mockResolvedValue({ messageId: "test-message-id" });

        const result = await emailService.sendNotificationEmail(specialCharNotification);

        expect(result).toBe(true);
        expect(mockSendMail).toHaveBeenCalledWith({
          from: "<EMAIL>",
          to: "<EMAIL>",
          subject: "NS Shop Admin - Thông báo đặc biệt với ký tự Việt Nam",
          html: expect.stringContaining("Thông báo đặc biệt với ký tự Việt Nam"),
        });
      });
    });

    describe("sendBulkNotificationEmails", () => {
      const mockBulkData = [
        {
          notification: mockNotificationData.notification,
          recipient: { name: "User 1", email: "<EMAIL>" },
        },
        {
          notification: mockNotificationData.notification,
          recipient: { name: "User 2", email: "<EMAIL>" },
        },
      ];

      it("should send bulk emails successfully", async () => {
        mockSendMail.mockResolvedValue({ messageId: "test-message-id" });

        const results = await emailService.sendBulkNotificationEmails(mockBulkData);

        expect(results.successful).toBe(2);
        expect(results.failed).toBe(0);
        expect(mockSendMail).toHaveBeenCalledTimes(2);
      });

      it("should handle partial failures in bulk sending", async () => {
        mockSendMail
          .mockResolvedValueOnce({ messageId: "test-message-id-1" })
          .mockRejectedValueOnce(new Error("SMTP error"));

        const results = await emailService.sendBulkNotificationEmails(mockBulkData);

        expect(results.successful).toBe(1);
        expect(results.failed).toBe(1);
        expect(results.errors).toHaveLength(1);
        expect(results.errors[0]).toContain("<EMAIL>");
      });

      it("should handle empty bulk data", async () => {
        const results = await emailService.sendBulkNotificationEmails([]);

        expect(results.successful).toBe(0);
        expect(results.failed).toBe(0);
        expect(results.errors).toHaveLength(0);
        expect(mockSendMail).not.toHaveBeenCalled();
      });
    });
  });

  describe("Configuration Validation", () => {
    it("should validate required SMTP configuration", () => {
      const validConfig = {
        SMTP_HOST: "smtp.gmail.com",
        SMTP_PORT: "587",
        SMTP_USER: "<EMAIL>",
        SMTP_PASS: "password",
        SMTP_FROM: "<EMAIL>",
      };

      const isValid = (emailService as any).validateConfiguration(validConfig);
      expect(isValid).toBe(true);
    });

    it("should reject invalid configuration", () => {
      const invalidConfigs = [
        {}, // empty config
        { SMTP_HOST: "smtp.gmail.com" }, // missing other fields
        { SMTP_HOST: "", SMTP_USER: "<EMAIL>" }, // empty host
      ];

      invalidConfigs.forEach(config => {
        const isValid = (emailService as any).validateConfiguration(config);
        expect(isValid).toBe(false);
      });
    });
  });
});
