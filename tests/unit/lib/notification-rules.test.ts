/**
 * Notification Rules Engine Unit Tests
 * Ki<PERSON>m tra unit cho notification rules engine
 */

import { NotificationRulesEngine, NotificationRule, NotificationEvent } from "@/lib/notification-rules";
import { prisma } from "@/lib/prisma";
import { sendNotificationEmails } from "@/lib/email-service";

// Mock dependencies
jest.mock("@/lib/prisma", () => ({
  prisma: {
    notification: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock("@/lib/email-service", () => ({
  sendNotificationEmails: jest.fn(),
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockSendNotificationEmails = sendNotificationEmails as jest.MockedFunction<typeof sendNotificationEmails>;

describe("NotificationRulesEngine", () => {
  let rulesEngine: NotificationRulesEngine;

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset singleton instance
    (NotificationRulesEngine as any).instance = undefined;
    rulesEngine = NotificationRulesEngine.getInstance();
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("Singleton Pattern", () => {
    it("should return the same instance", () => {
      const instance1 = NotificationRulesEngine.getInstance();
      const instance2 = NotificationRulesEngine.getInstance();

      expect(instance1).toBe(instance2);
    });
  });

  describe("Rule Management", () => {
    const mockRule: NotificationRule = {
      id: "rule-1",
      name: "Low Stock Alert",
      description: "Alert when product stock is low",
      eventType: "product.stock_low",
      conditions: {
        stock: { operator: "<=", value: 10 }
      },
      notificationTemplate: {
        title: "Low Stock Alert",
        message: "Product {{productName}} has low stock: {{stock}}",
        type: "WARNING",
        priority: "HIGH",
        targetType: "ALL_ADMINS",
        actionUrl: "/admin/products/{{productId}}"
      },
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    describe("addRule", () => {
      it("should add a new rule", async () => {
        await rulesEngine.addRule(mockRule);

        // Verify rule was added to internal map
        const rules = (rulesEngine as any).rules.get("product.stock_low");
        expect(rules).toContain(mockRule);
      });

      it("should handle multiple rules for same event type", async () => {
        const rule2 = { ...mockRule, id: "rule-2", name: "Critical Stock Alert" };

        await rulesEngine.addRule(mockRule);
        await rulesEngine.addRule(rule2);

        const rules = (rulesEngine as any).rules.get("product.stock_low");
        expect(rules).toHaveLength(2);
        expect(rules).toContain(mockRule);
        expect(rules).toContain(rule2);
      });
    });

    describe("removeRule", () => {
      it("should remove an existing rule", async () => {
        await rulesEngine.addRule(mockRule);
        await rulesEngine.removeRule("rule-1");

        const rules = (rulesEngine as any).rules.get("product.stock_low");
        expect(rules).not.toContain(mockRule);
      });

      it("should handle removing non-existent rule", async () => {
        // Should not throw error
        await expect(rulesEngine.removeRule("non-existent")).resolves.not.toThrow();
      });
    });

    describe("updateRule", () => {
      it("should update an existing rule", async () => {
        await rulesEngine.addRule(mockRule);

        const updatedRule = { ...mockRule, name: "Updated Rule Name" };
        await rulesEngine.updateRule(updatedRule);

        const rules = (rulesEngine as any).rules.get("product.stock_low");
        const foundRule = rules.find((r: NotificationRule) => r.id === "rule-1");
        expect(foundRule?.name).toBe("Updated Rule Name");
      });
    });
  });

  describe("Event Processing", () => {
    const mockEvent: NotificationEvent = {
      type: "product.stock_low",
      data: {
        productId: "product-123",
        productName: "Test Product",
        stock: 5,
      },
      timestamp: new Date(),
    };

    beforeEach(async () => {
      const mockRule: NotificationRule = {
        id: "rule-1",
        name: "Low Stock Alert",
        description: "Alert when product stock is low",
        eventType: "product.stock_low",
        conditions: {
          stock: { operator: "<=", value: 10 }
        },
        notificationTemplate: {
          title: "Low Stock Alert",
          message: "Product {{productName}} has low stock: {{stock}}",
          type: "WARNING",
          priority: "HIGH",
          targetType: "ALL_ADMINS",
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await rulesEngine.addRule(mockRule);
    });

    describe("processEvent", () => {
      it("should process event and trigger matching rules", async () => {
        mockPrisma.notification.create.mockResolvedValue({
          id: "notification-123",
          title: "Low Stock Alert",
          message: "Product Test Product has low stock: 5",
          type: "WARNING",
          priority: "HIGH",
          targetType: "ALL_ADMINS",
          isRead: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        } as any);

        await rulesEngine.processEvent(mockEvent);

        expect(mockPrisma.notification.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            title: "Low Stock Alert",
            message: "Product Test Product has low stock: 5",
            type: "WARNING",
            priority: "HIGH",
            targetType: "ALL_ADMINS",
          }),
        });
      });

      it("should not trigger inactive rules", async () => {
        // Add inactive rule
        const inactiveRule: NotificationRule = {
          id: "rule-2",
          name: "Inactive Rule",
          description: "This rule is inactive",
          eventType: "product.stock_low",
          conditions: {},
          notificationTemplate: {
            title: "Should not trigger",
            message: "This should not be created",
            type: "INFO",
            priority: "NORMAL",
            targetType: "ALL_ADMINS",
          },
          isActive: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        await rulesEngine.addRule(inactiveRule);
        await rulesEngine.processEvent(mockEvent);

        // Should only create one notification (from active rule)
        expect(mockPrisma.notification.create).toHaveBeenCalledTimes(1);
      });

      it("should handle events with no matching rules", async () => {
        const unmatchedEvent: NotificationEvent = {
          type: "unknown.event",
          data: {},
          timestamp: new Date(),
        };

        await rulesEngine.processEvent(unmatchedEvent);

        expect(mockPrisma.notification.create).not.toHaveBeenCalled();
      });

      it("should handle rule processing errors gracefully", async () => {
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
        mockPrisma.notification.create.mockRejectedValue(new Error("Database error"));

        await rulesEngine.processEvent(mockEvent);

        expect(consoleErrorSpy).toHaveBeenCalledWith(
          expect.stringContaining("Error processing rule"),
          expect.any(Error)
        );
      });
    });

    describe("Condition Evaluation", () => {
      it("should evaluate simple conditions correctly", () => {
        const conditions = {
          stock: { operator: "<=", value: 10 }
        };
        const data = { stock: 5 };

        const result = (rulesEngine as any).evaluateConditions(conditions, data);
        expect(result).toBe(true);
      });

      it("should evaluate multiple conditions with AND logic", () => {
        const conditions = {
          stock: { operator: "<=", value: 10 },
          category: { operator: "==", value: "electronics" }
        };
        const data = { stock: 5, category: "electronics" };

        const result = (rulesEngine as any).evaluateConditions(conditions, data);
        expect(result).toBe(true);
      });

      it("should return false when conditions don't match", () => {
        const conditions = {
          stock: { operator: "<=", value: 10 }
        };
        const data = { stock: 15 };

        const result = (rulesEngine as any).evaluateConditions(conditions, data);
        expect(result).toBe(false);
      });

      it("should handle missing data fields", () => {
        const conditions = {
          stock: { operator: "<=", value: 10 }
        };
        const data = { name: "Test Product" }; // missing stock field

        const result = (rulesEngine as any).evaluateConditions(conditions, data);
        expect(result).toBe(false);
      });
    });

    describe("Template Processing", () => {
      it("should replace template variables correctly", () => {
        const template = "Product {{productName}} has {{stock}} items left";
        const data = { productName: "Test Product", stock: 5 };

        const result = (rulesEngine as any).processTemplate(template, data);
        expect(result).toBe("Product Test Product has 5 items left");
      });

      it("should handle missing template variables", () => {
        const template = "Product {{productName}} has {{stock}} items left";
        const data = { productName: "Test Product" }; // missing stock

        const result = (rulesEngine as any).processTemplate(template, data);
        expect(result).toBe("Product Test Product has {{stock}} items left");
      });

      it("should handle templates without variables", () => {
        const template = "Static message";
        const data = { productName: "Test Product" };

        const result = (rulesEngine as any).processTemplate(template, data);
        expect(result).toBe("Static message");
      });
    });
  });
});
