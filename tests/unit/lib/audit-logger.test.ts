/**
 * Audit Logger Unit Tests
 * Kiểm tra unit cho audit logging functions
 */

import { logAdminAction, getRequestMetadata, AUDIT_ACTIONS, AUDIT_RESOURCES } from "@/lib/audit-logger";
import { prisma } from "@/lib/prisma";
import { NextRequest } from "next/server";

// Mock Prisma
jest.mock("@/lib/prisma", () => ({
  prisma: {
    auditLog: {
      create: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
    },
  },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe("Audit Logger", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset console.error mock
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("logAdminAction", () => {
    const mockAuditData = {
      action: "UPDATE",
      resource: "Product",
      resourceId: "product-123",
      oldValues: { name: "Old Product" },
      newValues: { name: "New Product" },
      description: "Updated product name",
      adminId: "admin-123",
      ipAddress: "***********",
      userAgent: "Mozilla/5.0",
    };

    it("should successfully log admin action", async () => {
      mockPrisma.auditLog.create.mockResolvedValue({
        id: "audit-123",
        ...mockAuditData,
        createdAt: new Date(),
      } as any);

      await logAdminAction(mockAuditData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: {
          action: mockAuditData.action,
          resource: mockAuditData.resource,
          resourceId: mockAuditData.resourceId,
          oldValues: mockAuditData.oldValues,
          newValues: mockAuditData.newValues,
          description: mockAuditData.description,
          adminId: mockAuditData.adminId,
          ipAddress: mockAuditData.ipAddress,
          userAgent: mockAuditData.userAgent,
        },
      });
    });

    it("should handle missing optional fields", async () => {
      const minimalData = {
        action: "CREATE",
        resource: "User",
        adminId: "admin-123",
      };

      mockPrisma.auditLog.create.mockResolvedValue({
        id: "audit-123",
        ...minimalData,
        createdAt: new Date(),
      } as any);

      await logAdminAction(minimalData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: {
          action: minimalData.action,
          resource: minimalData.resource,
          resourceId: undefined,
          oldValues: undefined,
          newValues: undefined,
          description: undefined,
          adminId: minimalData.adminId,
          ipAddress: "unknown",
          userAgent: "unknown",
        },
      });
    });

    it("should handle database errors gracefully", async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      mockPrisma.auditLog.create.mockRejectedValue(new Error("Database error"));

      // Should not throw error
      await expect(logAdminAction(mockAuditData)).resolves.not.toThrow();

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Failed to log admin action:",
        expect.any(Error)
      );
    });

    it("should set default values for missing IP and User Agent", async () => {
      const dataWithoutMetadata = {
        action: "DELETE",
        resource: "Category",
        adminId: "admin-123",
      };

      await logAdminAction(dataWithoutMetadata);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          ipAddress: "unknown",
          userAgent: "unknown",
        }),
      });
    });
  });

  describe("getRequestMetadata", () => {
    it("should extract IP address from x-forwarded-for header", () => {
      const request = new NextRequest("http://localhost:3000/api/test", {
        headers: {
          "x-forwarded-for": "***********, ********",
        },
      });

      const metadata = getRequestMetadata(request);

      expect(metadata.ipAddress).toBe("***********, ********");
    });

    it("should extract IP address from x-real-ip header", () => {
      const request = new NextRequest("http://localhost:3000/api/test", {
        headers: {
          "x-real-ip": "***********",
        },
      });

      const metadata = getRequestMetadata(request);

      expect(metadata.ipAddress).toBe("***********");
    });

    it("should extract user agent from headers", () => {
      const userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
      const request = new NextRequest("http://localhost:3000/api/test", {
        headers: {
          "user-agent": userAgent,
        },
      });

      const metadata = getRequestMetadata(request);

      expect(metadata.userAgent).toBe(userAgent);
    });

    it("should return 'unknown' for missing headers", () => {
      const request = new NextRequest("http://localhost:3000/api/test");

      const metadata = getRequestMetadata(request);

      expect(metadata.ipAddress).toBe("unknown");
      expect(metadata.userAgent).toBe("unknown");
    });

    it("should prioritize x-forwarded-for over x-real-ip", () => {
      const request = new NextRequest("http://localhost:3000/api/test", {
        headers: {
          "x-forwarded-for": "***********",
          "x-real-ip": "********",
        },
      });

      const metadata = getRequestMetadata(request);

      expect(metadata.ipAddress).toBe("***********");
    });
  });

  describe("AUDIT_ACTIONS constants", () => {
    it("should contain all expected action types", () => {
      const expectedActions = [
        "CREATE", "UPDATE", "DELETE", "LOGIN", "LOGOUT", 
        "VIEW", "EXPORT", "IMPORT", "ACTIVATE", "DEACTIVATE",
        "APPROVE", "REJECT", "PUBLISH", "UNPUBLISH"
      ];

      expectedActions.forEach(action => {
        expect(AUDIT_ACTIONS).toHaveProperty(action);
        expect(AUDIT_ACTIONS[action as keyof typeof AUDIT_ACTIONS]).toBe(action);
      });
    });
  });

  describe("AUDIT_RESOURCES constants", () => {
    it("should contain all expected resource types", () => {
      const expectedResources = [
        "ADMIN_USER", "USER", "PRODUCT", "CATEGORY", "ORDER",
        "BRAND", "POST", "PAGE", "NOTIFICATION", "SETTING", "INVENTORY"
      ];

      expectedResources.forEach(resource => {
        expect(AUDIT_RESOURCES).toHaveProperty(resource);
      });
    });

    it("should have proper resource mappings", () => {
      expect(AUDIT_RESOURCES.ADMIN_USER).toBe("AdminUser");
      expect(AUDIT_RESOURCES.PRODUCT).toBe("Product");
      expect(AUDIT_RESOURCES.NOTIFICATION).toBe("Notification");
    });
  });
});
